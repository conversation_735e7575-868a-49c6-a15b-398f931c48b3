'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useToast } from '@/components/ui/use-toast'

interface AffiliateUser {
  id: number
  email: string
  firstName?: string
  lastName?: string
  username?: string
  status?: string
}

interface UseAffiliateAuthReturn {
  user: AffiliateUser | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<boolean>
  logout: () => Promise<void>
  checkAuth: () => Promise<void>
}

export function useAffiliateAuth(): UseAffiliateAuthReturn {
  const [user, setUser] = useState<AffiliateUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const { toast } = useToast()

  const checkAuth = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/affiliate/me', {
        method: 'GET',
        credentials: 'include',
      })

      if (response.ok) {
        const userData = await response.json()
        setUser(userData.user)
      } else {
        setUser(null)
      }
    } catch (error) {
      console.error('Auth check error:', error)
      setUser(null)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/affiliate/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
        credentials: 'include',
      })

      const result = await response.json()

      if (response.ok) {
        setUser(result.user)
        return true
      } else {
        toast({
          title: 'Login Failed',
          description: result.message || 'Invalid credentials',
          variant: 'destructive',
        })
        return false
      }
    } catch (error) {
      console.error('Login error:', error)
      toast({
        title: 'Login Failed',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      })
      return false
    }
  }

  const logout = async () => {
    try {
      await fetch('/api/affiliate/logout', {
        method: 'POST',
        credentials: 'include',
      })
      
      setUser(null)
      toast({
        title: 'Logged Out',
        description: 'You have been successfully logged out.',
      })
      
      router.push('/affiliate/login')
    } catch (error) {
      console.error('Logout error:', error)
      toast({
        title: 'Logout Error',
        description: 'An error occurred during logout.',
        variant: 'destructive',
      })
    }
  }

  useEffect(() => {
    checkAuth()
  }, [])

  return {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    logout,
    checkAuth,
  }
}
