'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { Event } from '@/payload-types'
import { SupportedLocale } from '@/config/app'

interface AffiliateUser {
  id: number
  email: string
  firstName?: string
  lastName?: string
  username?: string
  status?: string
}

interface AffiliateContextType {
  events: Event[]
  user: AffiliateUser | null
  isAuthenticated: boolean
  isLoading: boolean
  setUser: (user: AffiliateUser | null) => void
}

const AffiliateContext = createContext<AffiliateContextType | undefined>(undefined)

interface AffiliateProvidersProps {
  children: React.ReactNode
  initialEvents?: Event[]
  locale?: SupportedLocale
}

export function AffiliateProviders({ children, initialEvents = [] }: AffiliateProvidersProps) {
  const [user, setUser] = useState<AffiliateUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Check authentication on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/affiliate/me', {
          method: 'GET',
          credentials: 'include',
        })

        if (response.ok) {
          const userData = await response.json()
          setUser(userData.user)
        }
      } catch (error) {
        console.error('Auth check error:', error)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  const value: AffiliateContextType = {
    events: initialEvents,
    user,
    isAuthenticated: !!user,
    isLoading,
    setUser,
  }

  return <AffiliateContext.Provider value={value}>{children}</AffiliateContext.Provider>
}

export function useAffiliate(): AffiliateContextType {
  const context = useContext(AffiliateContext)
  if (context === undefined) {
    throw new Error('useAffiliate must be used within an AffiliateProvider')
  }
  return context
}

// Keep the old useEvents hook for backward compatibility
export function useEvents(): { events: Event[] } {
  const context = useContext(AffiliateContext)
  if (context === undefined) {
    throw new Error('useEvents must be used within an AffiliateProvider')
  }
  return { events: context.events }
}
