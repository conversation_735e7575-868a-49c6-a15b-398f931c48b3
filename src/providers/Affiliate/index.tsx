'use client'

import React, { createContext, useContext } from 'react'
import { Event } from '@/payload-types'
import { SupportedLocale } from '@/config/app'

interface EventsContextType {
  events: Event[]
}

const AffiliateContext = createContext<EventsContextType | undefined>(undefined)

interface AffiliateProvidersProps {
  children: React.ReactNode
  initialEvents?: Event[]
  locale?: SupportedLocale
}

export function AffiliateProviders({ children, initialEvents = [] }: AffiliateProvidersProps) {
  const value: EventsContextType = {
    events: initialEvents,
  }

  return <AffiliateContext.Provider value={value}>{children}</AffiliateContext.Provider>
}

export function useEvents(): EventsContextType {
  const context = useContext(AffiliateContext)
  if (context === undefined) {
    throw new Error('useEvents must be used within an EventsProvider')
  }
  return context
}
