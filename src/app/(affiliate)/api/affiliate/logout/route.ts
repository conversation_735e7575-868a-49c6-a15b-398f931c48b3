import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

export async function POST(req: NextRequest) {
  try {
    const cookieStore = await cookies()
    
    // Clear the authentication cookie
    cookieStore.delete('authToken')
    
    return NextResponse.json({
      message: 'Logout successful'
    })
  } catch (error) {
    console.error('Affiliate logout error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
