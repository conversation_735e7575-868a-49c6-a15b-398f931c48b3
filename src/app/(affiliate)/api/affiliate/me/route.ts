import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import { extractJWT } from '@/utilities/jwt'
import { JWT_USER_SECRET } from '@/config/jwt'
import { getPayload } from '@/payload-config/getPayloadConfig'

export async function GET(req: NextRequest) {
  try {
    const cookieStore = await cookies()
    const authToken = cookieStore.get('authToken')

    if (!authToken) {
      return NextResponse.json(
        { message: 'Not authenticated' },
        { status: 401 }
      )
    }

    // Verify JWT token
    const payload = await extractJWT(authToken.value, JWT_USER_SECRET)
    
    if (!payload || !payload.id || payload.role !== 'affiliate') {
      return NextResponse.json(
        { message: 'Invalid token' },
        { status: 401 }
      )
    }

    // Get user data from database
    const payloadCMS = await getPayload()
    const userRes = await payloadCMS.find({
      collection: 'users',
      where: { id: { equals: payload.id } },
      limit: 1,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        username: true,
        status: true
      },
    })

    const user = userRes.docs[0]
    
    if (!user) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        username: user.username,
      }
    })

  } catch (error) {
    console.error('Get user error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
