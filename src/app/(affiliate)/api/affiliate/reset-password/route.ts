import { NextRequest, NextResponse } from 'next/server'
import { generateSalt, hashPassword } from '@/utilities/password'
import { signJwtToken } from '@/app/(payload)/api/user/utils'
import { getPayload } from '@/payload-config/getPayloadConfig'

export async function POST(req: NextRequest) {
  try {
    const { token, password } = await req.json()
    
    if (!token || !password) {
      return NextResponse.json(
        { message: 'Token and new password are required' },
        { status: 400 }
      )
    }

    // Validate password strength
    if (password.length < 6) {
      return NextResponse.json(
        { message: 'Password must be at least 6 characters long' },
        { status: 400 }
      )
    }

    const payload = await getPayload()
    
    // Find user by reset token
    const userRes = await payload.find({
      collection: 'users',
      where: { resetPasswordToken: { equals: token } },
      limit: 1,
      select: { 
        id: true,
        email: true,
        resetPasswordExpiration: true, 
        resetPasswordToken: true,
      },
      showHiddenFields: true,
    })

    const user = userRes.docs[0]
    
    // Validate token and expiration
    if (
      !user ||
      !user.resetPasswordExpiration ||
      new Date(user.resetPasswordExpiration) < new Date()
    ) {
      return NextResponse.json(
        { message: 'Invalid or expired reset token' },
        { status: 400 }
      )
    }

    // Generate new salt and hash for the new password
    const salt = await generateSalt()
    const hash = await hashPassword(password, salt)

    // Update user with new password and clear reset token
    await payload.update({
      collection: 'users',
      id: user.id,
      data: {
        salt,
        hash,
        resetPasswordToken: null,
        resetPasswordExpiration: null,
      },
    })

    // Sign JWT token for automatic login
    await signJwtToken({
      fieldsToSign: { 
        id: user.id, 
        email: user.email,
        role: 'affiliate'
      },
    })

    return NextResponse.json({
      message: 'Password has been reset successfully. You are now logged in.',
      user: { 
        id: user.id, 
        email: user.email 
      },
    })

  } catch (error) {
    console.error('Affiliate reset password error:', error)
    return NextResponse.json(
      { message: 'Failed to reset password' },
      { status: 500 }
    )
  }
}
