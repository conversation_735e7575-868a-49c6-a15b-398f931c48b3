import { NextRequest, NextResponse } from 'next/server'
import { sendMailAndWriteLog } from '@/collections/Emails/utils'
import { getServerSideURL } from '@/utilities/getURL'
import { getPayload } from '@/payload-config/getPayloadConfig'

function generateResetToken(length = 48) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let token = ''
  for (let i = 0; i < length; i++) {
    token += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return token
}

export async function POST(req: NextRequest) {
  try {
    const { email } = await req.json()

    if (!email) {
      return NextResponse.json({ message: 'Email is required' }, { status: 400 })
    }

    const payload = await getPayload()

    // Find user by email
    const userRes = await payload.find({
      collection: 'users',
      where: {
        email: { equals: email.toLowerCase() },
        role: {
          equals: 'affiliate',
        },
      },
      limit: 1,
    })

    const user = userRes.docs[0]

    console.log('user', user)

    if (!user) {
      // For security, do not reveal if user exists
      return NextResponse.json({
        message: 'If the email exists in our affiliate system, a reset link will be sent.',
      })
    }

    // Generate reset token and expiration
    const resetToken = generateResetToken()
    const resetExpiration = new Date(Date.now() + 1000 * 60 * 60).toISOString() // 1 hour

    // Update user with reset token
    await payload.update({
      collection: 'users',
      id: user.id,
      data: {
        resetPasswordToken: resetToken,
        resetPasswordExpiration: resetExpiration,
      },
    })

    // Build affiliate-specific reset link
    const resetLink = `${getServerSideURL() || 'http://localhost:3000'}/affiliate/reset-password?token=${resetToken}`

    // Create affiliate-specific email template
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #1f2937; margin-bottom: 10px;">Orchestars Affiliate Portal</h1>
          <h2 style="color: #4b5563; font-weight: normal;">Password Reset Request</h2>
        </div>
        
        <div style="background-color: #f9fafb; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <p style="color: #374151; line-height: 1.6; margin-bottom: 15px;">
            Hello ${user.firstName ? user.firstName : 'Affiliate Partner'},
          </p>
          <p style="color: #374151; line-height: 1.6; margin-bottom: 15px;">
            You are receiving this email because you (or someone else) have requested a password reset for your affiliate account.
          </p>
          <p style="color: #374151; line-height: 1.6; margin-bottom: 20px;">
            Please click the button below to reset your password. This link will expire in 1 hour for security reasons.
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetLink}" 
               style="background-color: #1f2937; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
              Reset Password
            </a>
          </div>
          
          <p style="color: #6b7280; font-size: 14px; line-height: 1.6;">
            If the button doesn't work, you can copy and paste this link into your browser:<br>
            <a href="${resetLink}" style="color: #3b82f6; word-break: break-all;">${resetLink}</a>
          </p>
        </div>
        
        <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; color: #6b7280; font-size: 14px;">
          <p style="margin-bottom: 10px;">
            <strong>Security Notice:</strong> If you did not request this password reset, please ignore this email. Your password will remain unchanged.
          </p>
          <p style="margin-bottom: 0;">
            For support, contact us at <a href="mailto:<EMAIL>" style="color: #3b82f6;"><EMAIL></a>
          </p>
        </div>
      </div>
    `

    // Send email
    await sendMailAndWriteLog({
      resendMailData: {
        to: email,
        subject: 'Affiliate Portal - Password Reset Request',
        html,
      },
      emailData: { user: user.id },
      payload,
    })

    return NextResponse.json({
      message: 'If the email exists in our affiliate system, a reset link will be sent.',
    })
  } catch (error) {
    console.error('Affiliate forgot password error:', error)
    return NextResponse.json({ message: 'Failed to process request' }, { status: 500 })
  }
}
