'use client'

import React, { useMemo, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Copy, Link2, Plus, X } from 'lucide-react'
import { useToast } from '@/components/ui/use-toast'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { useEvents } from '@/providers/Affiliate'

// Zod validation schema
const affiliateLinkSchema = z.object({
  affiliateCode: z
    .string()
    .min(3, 'Affiliate code must be at least 3 characters')
    .max(50, 'Affiliate code must not exceed 50 characters')
    .regex(
      /^[a-zA-Z0-9_-]+$/,
      'Affiliate code can only contain letters, numbers, underscores, and hyphens',
    )
    .nonempty('Affiliate code is required'),
  utmParams: z.object({
    utm_source: z.string().max(100, 'UTM Source must not exceed 100 characters').optional(),
    utm_medium: z.string().max(100, 'UTM Medium must not exceed 100 characters').optional(),
    utm_campaign: z.string().max(100, 'UTM Campaign must not exceed 100 characters').optional(),
    utm_term: z.string().max(100, 'UTM Term must not exceed 100 characters').optional(),
    utm_content: z.string().max(100, 'UTM Content must not exceed 100 characters').optional(),
  }),
  event: z.string().optional(),
  promotionCode: z.string().max(50, 'Promotion code must not exceed 50 characters').optional(),
})

type AffiliateLinkFormData = z.infer<typeof affiliateLinkSchema>

interface AffiliateLink {
  id: number
  affiliateCode: string
  promotionCode?: string | null
  utmParams?: {
    source?: string
    medium?: string
    campaign?: string
    term?: string
    content?: string
  } | null
  targetLink?: string | null
  generatedUrl?: string
  status: 'active' | 'disabled'
  createdAt: string
}

interface CreateAffiliateLinkRequest {
  affiliateCode: string
  targetLink: string
  utmParams: {
    source: string
    medium: string
    campaign: string
    term?: string
    content?: string
  }
  event?: number
  promotionCode?: string
}

interface ApiResponse {
  success: boolean
  data?: AffiliateLink
  error?: string
  details?: Array<{ field: string; message: string }>
}

export function CreateAffiliateLink() {
  const { toast } = useToast()
  const [isCreating, setIsCreating] = useState(false)
  const [generatedLinks, setGeneratedLinks] = useState<AffiliateLink[]>([])

  // Get events data from context
  const { events } = useEvents()

  console.log('events', events)

  const eventOptions = useMemo(() => {
    return events.map((event) => ({
      value: event.id.toString(),
      label: event.title || 'Untitled Event',
      startDatetime: event.startDatetime,
    }))
  }, [events])

  // Initialize React Hook Form with Zod validation
  const form = useForm<AffiliateLinkFormData>({
    resolver: zodResolver(affiliateLinkSchema),
    defaultValues: {
      affiliateCode: '',
      utmParams: {
        utm_source: '',
        utm_medium: 'affiliate',
        utm_campaign: '',
        utm_term: '',
        utm_content: '',
      },
      event: '',
      promotionCode: '',
    },
  })

  const { watch, formState } = form
  const watchedValues = watch()
  const { isSubmitting } = formState

  // Generate target link based on form data
  const generateTargetLink = () => {
    const params = new URLSearchParams()

    let url = 'https://orchestars.com/events'

    console.log('watchedValues', watchedValues)

    if (watchedValues.event) {
      const selectedEvent = events.find((evt) => String(evt.id) === watchedValues.event)
      if (selectedEvent) {
        url = `https://orchestars.com/events/${selectedEvent.slug}`
      }
    }

    if (watchedValues.affiliateCode) {
      params.append('affiliate', watchedValues.affiliateCode)
    }
    if (watchedValues.promotionCode) {
      params.append('promo_code', watchedValues.promotionCode)
    }

    if (watchedValues.utmParams?.utm_source) {
      params.append('utm_source', watchedValues.utmParams.utm_source)
    }

    if (watchedValues.utmParams?.utm_medium) {
      params.append('utm_medium', watchedValues.utmParams.utm_medium)
    }
    if (watchedValues.utmParams?.utm_campaign) {
      params.append('utm_campaign', watchedValues.utmParams.utm_campaign)
    }
    if (watchedValues.utmParams?.utm_term) {
      params.append('utm_term', watchedValues.utmParams.utm_term)
    }
    if (watchedValues.utmParams?.utm_content) {
      params.append('utm_content', watchedValues.utmParams.utm_content)
    }

    return `${url}?${params.toString()}`
  }

  const onSubmit = async (data: AffiliateLinkFormData) => {
    setIsCreating(true)

    try {
      const requestData: CreateAffiliateLinkRequest = {
        affiliateCode: data.affiliateCode,
        targetLink: generateTargetLink(),
        utmParams: {
          source: data.utmParams.utm_source || '',
          medium: data.utmParams.utm_medium || '',
          campaign: data.utmParams.utm_campaign || '',
          term: data.utmParams.utm_term || undefined,
          content: data.utmParams.utm_content || undefined,
        },
        event: data.event ? Number(data.event) : undefined,
        promotionCode: data.promotionCode || undefined,
      }

      const response = await fetch('/api/affiliate/link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      })

      const result: ApiResponse = await response.json()

      if (!result.success) {
        if (result.details) {
          // Handle validation errors from server
          result.details.forEach((detail) => {
            const fieldName = detail.field as keyof AffiliateLinkFormData
            form.setError(fieldName, {
              type: 'server',
              message: detail.message,
            })
          })

          toast({
            title: 'Validation Error',
            description: 'Please check the form for errors.',
            variant: 'destructive',
          })
        } else {
          toast({
            title: 'Error',
            description: result.error || 'Failed to create affiliate link.',
            variant: 'destructive',
          })
        }
        return
      }

      if (result.data) {
        setGeneratedLinks((prev) => [result.data!, ...prev])

        toast({
          title: 'Affiliate Link Created',
          description: 'Your affiliate link has been generated successfully.',
        })

        // Reset form
        form.reset()
      }
    } catch (error) {
      console.error('Error creating affiliate link:', error)
      toast({
        title: 'Error',
        description: 'Failed to create affiliate link. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsCreating(false)
    }
  }

  const copyToClipboard = (url: string) => {
    navigator.clipboard.writeText(url)
    toast({
      title: 'Copied to Clipboard',
      description: 'Affiliate link has been copied to your clipboard.',
    })
  }

  const removeLink = (id: number) => {
    setGeneratedLinks((prev) => prev.filter((link) => link.id !== id))
  }

  return (
    <div className="space-y-6">
      <Card className="shadow-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Link2 className="h-5 w-5" />
            Create Affiliate Link
          </CardTitle>
          <CardDescription>
            Generate trackable affiliate links with UTM parameters for marketing campaigns
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Affiliate Code Section */}
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="affiliateCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Affiliate Code *</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., kol123, influencer456" {...field} />
                      </FormControl>
                      <FormDescription>
                        Enter your unique affiliate identifier (e.g., kol123, influencer456)
                      </FormDescription>
                      <FormMessage className='text-red-600' />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="event"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target Event (Optional)</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={'Select an event (optional)'} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-white">
                          {/* <SelectItem value="">No specific event</SelectItem> */}
                          {eventOptions.map((event) => (
                            <SelectItem key={event.value} value={event.value}>
                              {event.label}
                              {event.startDatetime && (
                                <span className="text-xs text-muted-foreground ml-2">
                                  ({new Date(event.startDatetime).toLocaleDateString()})
                                </span>
                              )}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage className='text-red-600' />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="promotionCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Promotion Code (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., SUMMER20, EARLYBIRD" {...field} />
                      </FormControl>
                      <FormMessage className='text-red-600' />
                    </FormItem>
                  )}
                />
              </div>

              {/* UTM Parameters Section */}
              <div className="space-y-4">
                <div className="border-t pt-4">
                  <h3 className="text-lg font-semibold mb-4">UTM Tracking Parameters</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Configure UTM parameters for tracking your affiliate link performance
                  </p>

                  <div className="grid gap-4 md:grid-cols-3">
                    <FormField
                      control={form.control}
                      name="utmParams.utm_source"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>UTM Source (Optional)</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., facebook, google, newsletter" {...field} />
                          </FormControl>
                          <FormMessage className='text-red-600' />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="utmParams.utm_medium"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>UTM Medium (Optional)</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent className="bg-white">
                              <SelectItem value="affiliate">Affiliate</SelectItem>
                              <SelectItem value="social">Social</SelectItem>
                              <SelectItem value="email">Email</SelectItem>
                              <SelectItem value="cpc">CPC</SelectItem>
                              <SelectItem value="banner">Banner</SelectItem>
                              <SelectItem value="referral">Referral</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage className='text-red-600' />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="utmParams.utm_campaign"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>UTM Campaign (Optional)</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., summer-promo, holiday-sale" {...field} />
                          </FormControl>
                          <FormMessage className='text-red-600' />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid gap-4 md:grid-cols-2 mt-4">
                    <FormField
                      control={form.control}
                      name="utmParams.utm_term"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>UTM Term (Optional)</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., classical music, orchestra" {...field} />
                          </FormControl>
                          <FormMessage className='text-red-600' />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="utmParams.utm_content"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>UTM Content (Optional)</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., banner-top, text-link" {...field} />
                          </FormControl>
                          <FormMessage className='text-red-600' />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>

              {/* Generated Target URL Preview */}
              <div className="space-y-4 border-t pt-4">
                <div className="space-y-2">
                  <Label htmlFor="targetUrl">Generated Target URL (Preview)</Label>
                  <Input
                    id="targetUrl"
                    value={generateTargetLink()}
                    readOnly
                    className="bg-muted text-muted-foreground cursor-not-allowed"
                  />
                  <p className="text-xs text-muted-foreground">
                    This URL will be generated automatically based on your UTM parameters and
                    affiliate code
                  </p>
                </div>
              </div>

              <Button type="submit" variant={'secondary'} className="w-full" disabled={isCreating || isSubmitting}>
                <Plus className="h-4 w-4 mr-2" />
                {isSubmitting ? 'Generating...' : 'Generate Affiliate Link'}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      {generatedLinks.length > 0 && (
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle>Generated Links</CardTitle>
            <CardDescription>Your recently created affiliate links</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {generatedLinks.map((link) => (
                <div key={link.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <h4 className="font-medium">Affiliate Code: {link.affiliateCode}</h4>
                      <p className="text-sm text-muted-foreground">Target: {link.targetLink}</p>
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => removeLink(link.id)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    {link.utmParams?.source && (
                      <Badge variant="secondary">Source: {link.utmParams.source}</Badge>
                    )}
                    {link.utmParams?.medium && (
                      <Badge variant="secondary">Medium: {link.utmParams.medium}</Badge>
                    )}
                    {link.utmParams?.campaign && (
                      <Badge variant="secondary">Campaign: {link.utmParams.campaign}</Badge>
                    )}
                    {link.utmParams?.term && (
                      <Badge variant="outline">Term: {link.utmParams.term}</Badge>
                    )}
                    {link.utmParams?.content && (
                      <Badge variant="outline">Content: {link.utmParams.content}</Badge>
                    )}
                    {link.promotionCode && (
                      <Badge variant="outline">Promo: {link.promotionCode}</Badge>
                    )}
                  </div>

                  <div className="flex items-center gap-2 p-2 bg-muted rounded">
                    <code className="flex-1 text-sm break-all">
                      {link.generatedUrl || link.targetLink}
                    </code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(link.generatedUrl || link.targetLink || '')}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>

                  <p className="text-xs text-muted-foreground">
                    Created: {new Date(link.createdAt).toLocaleString()}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
